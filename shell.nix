{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Build tools
    gcc
    cmake
    pkg-config
    xmake
    
    # Graphics libraries
    glfw
    glew
    mesa
    libGL
    
    # ImGui (we'll build it ourselves or use vcpkg)
    # imgui is available in nixpkgs but might need specific configuration
    
    # X11 libraries for Linux
    xorg.libX11
    xorg.libXrandr
    xorg.libXi
    xorg.libXcursor
    xorg.libXinerama
    
    # Additional development tools
    gdb
    valgrind
  ];

  shellHook = ''
    echo "Fluid Simulation Development Environment"
    echo "Available tools:"
    echo "  - xmake (build system)"
    echo "  - gcc (compiler)"
    echo "  - gdb (debugger)"
    echo "  - valgrind (memory checker)"
    echo ""
    echo "Graphics libraries:"
    echo "  - GLFW: ${pkgs.glfw}"
    echo "  - GLEW: ${pkgs.glew}"
    echo "  - Mesa: ${pkgs.mesa}"
    echo ""
    echo "To build the project: xmake"
    echo "To run the project: xmake run"
    
    # Set environment variables for pkg-config
    export PKG_CONFIG_PATH="${pkgs.glfw}/lib/pkgconfig:${pkgs.glew}/lib/pkgconfig:$PKG_CONFIG_PATH"
    
    # Set library paths
    export LD_LIBRARY_PATH="${pkgs.glfw}/lib:${pkgs.glew}/lib:${pkgs.mesa}/lib:$LD_LIBRARY_PATH"
    
    # Set include paths
    export C_INCLUDE_PATH="${pkgs.glfw}/include:${pkgs.glew}/include:${pkgs.mesa}/include:$C_INCLUDE_PATH"
    export CPLUS_INCLUDE_PATH="${pkgs.glfw}/include:${pkgs.glew}/include:${pkgs.mesa}/include:$CPLUS_INCLUDE_PATH"
  '';
}
