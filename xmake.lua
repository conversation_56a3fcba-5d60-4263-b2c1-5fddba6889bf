add_rules("mode.debug", "mode.release")

-- For NixOS, we'll use system packages via pkg-config
target("fluid-simulation")
    set_kind("binary")
    set_languages("c++17")
    add_files("src/*.cpp")

    -- Use pkg-config to find system libraries
    add_packages("pkgconf")

    -- Add system libraries for Linux/NixOS
    if is_plat("linux") then
        -- OpenGL and windowing
        add_syslinks("GL", "GLEW", "glfw", "pthread")

        -- X11 libraries
        add_syslinks("X11", "Xrandr", "Xi", "Xcursor", "Xinerama", "dl")

        -- Try to use pkg-config for better library detection
        on_load(function (target)
            -- Try to find libraries via pkg-config
            local glfw_cflags = os.iorun("pkg-config --cflags glfw3 2>/dev/null")
            local glfw_libs = os.iorun("pkg-config --libs glfw3 2>/dev/null")

            if glfw_cflags and glfw_cflags ~= "" then
                target:add("cxflags", glfw_cflags:trim())
            end
            if glfw_libs and glfw_libs ~= "" then
                target:add("ldflags", glfw_libs:trim())
            end

            local glew_cflags = os.iorun("pkg-config --cflags glew 2>/dev/null")
            local glew_libs = os.iorun("pkg-config --libs glew 2>/dev/null")

            if glew_cflags and glew_cflags ~= "" then
                target:add("cxflags", glew_cflags:trim())
            end
            if glew_libs and glew_libs ~= "" then
                target:add("ldflags", glew_libs:trim())
            end
        end)
    end

    -- Compiler flags
    add_cxxflags("-Wall", "-Wextra")
    if is_mode("debug") then
        add_defines("DEBUG")
        set_symbols("debug")
        set_optimize("none")
    else
        set_optimize("fastest")
        add_defines("NDEBUG")
    end

--
-- If you want to known more usage about xmake, please see https://xmake.io
--
-- ## FAQ
--
-- You can enter the project directory firstly before building project.
--
--   $ cd projectdir
--
-- 1. How to build project?
--
--   $ xmake
--
-- 2. How to configure project?
--
--   $ xmake f -p [macosx|linux|iphoneos ..] -a [x86_64|i386|arm64 ..] -m [debug|release]
--
-- 3. Where is the build output directory?
--
--   The default output directory is `./build` and you can configure the output directory.
--
--   $ xmake f -o outputdir
--   $ xmake
--
-- 4. How to run and debug target after building project?
--
--   $ xmake run [targetname]
--   $ xmake run -d [targetname]
--
-- 5. How to install target to the system directory or other output directory?
--
--   $ xmake install
--   $ xmake install -o installdir
--
-- 6. Add some frequently-used compilation flags in xmake.lua
--
-- @code
--    -- add debug and release modes
--    add_rules("mode.debug", "mode.release")
--
--    -- add macro definition
--    add_defines("NDEBUG", "_GNU_SOURCE=1")
--
--    -- set warning all as error
--    set_warnings("all", "error")
--
--    -- set language: c99, c++11
--    set_languages("c99", "c++11")
--
--    -- set optimization: none, faster, fastest, smallest
--    set_optimize("fastest")
--
--    -- add include search directories
--    add_includedirs("/usr/include", "/usr/local/include")
--
--    -- add link libraries and search directories
--    add_links("tbox")
--    add_linkdirs("/usr/local/lib", "/usr/lib")
--
--    -- add system link libraries
--    add_syslinks("z", "pthread")
--
--    -- add compilation and link flags
--    add_cxflags("-stdnolib", "-fno-strict-aliasing")
--    add_ldflags("-L/usr/local/lib", "-lpthread", {force = true})
--
-- @endcode
--

